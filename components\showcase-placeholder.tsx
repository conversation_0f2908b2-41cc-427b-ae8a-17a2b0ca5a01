export default function ShowcasePlaceholder() {
  return (
    <div className="relative overflow-hidden rounded-lg border border-zinc-800 bg-zinc-900 shadow-2xl">
      {/* Top Bar */}
      <div className="flex items-center justify-between border-b border-zinc-800 bg-zinc-900 px-3 py-2 sm:px-4 sm:py-3">
        <div className="flex items-center gap-2 sm:gap-3">
          <div className="flex gap-1 sm:gap-1.5">
            <div className="h-2.5 w-2.5 rounded-full bg-red-500/80 sm:h-3 sm:w-3"></div>
            <div className="h-2.5 w-2.5 rounded-full bg-yellow-500/80 sm:h-3 sm:w-3"></div>
            <div className="h-2.5 w-2.5 rounded-full bg-green-500/80 sm:h-3 sm:w-3"></div>
          </div>
          <div className="text-xs font-medium text-zinc-400 sm:text-sm">
            Next Core
          </div>
        </div>
        <div className="h-5 w-20 rounded bg-zinc-800 sm:h-6 sm:w-32"></div>
      </div>

      {/* Main Interface */}
      <div className="flex">
        {/* Sidebar - Hidden on mobile, visible on desktop */}
        <div className="hidden w-48 border-r border-zinc-800 bg-zinc-900 p-3 sm:block sm:w-64 sm:p-4">
          <div className="space-y-3">
            <div className="h-3.5 w-16 rounded bg-zinc-700 sm:h-4 sm:w-20"></div>
            <div className="space-y-2 sm:space-y-3">
              <div className="h-2.5 w-full rounded bg-zinc-800 sm:h-3"></div>
              <div className="h-2.5 w-3/4 rounded bg-zinc-800 sm:h-3"></div>
              <div className="h-2.5 w-5/6 rounded bg-zinc-800 sm:h-3"></div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-3 sm:p-6">
          <div className="space-y-4 sm:space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div className="h-5 w-32 rounded bg-zinc-700 sm:h-6 sm:w-48"></div>
              <div className="h-6 w-16 rounded bg-white sm:h-8 sm:w-24"></div>
            </div>

            {/* Issue List - Fewer items on mobile */}
            <div className="space-y-2 sm:space-y-3">
              {[...Array(6)].map((_, i) => (
                <div
                  key={i}
                  className={`flex items-center gap-2 border-b border-zinc-800 pb-2 sm:gap-3 sm:pb-3 ${
                    i >= 6 ? "hidden sm:flex" : ""
                  }`}
                >
                  <div className="h-3 w-3 rounded bg-zinc-700 sm:h-4 sm:w-4"></div>
                  <div className="h-3 flex-1 rounded bg-zinc-700 sm:h-4"></div>
                  <div className="h-3 w-12 rounded bg-zinc-600 sm:h-4 sm:w-16"></div>
                </div>
              ))}
              {/* Show additional items only on desktop */}
              {[...Array(4)].map((_, i) => (
                <div
                  key={i + 6}
                  className="hidden items-center gap-3 border-b border-zinc-800 pb-3 sm:flex"
                >
                  <div className="h-4 w-4 rounded bg-zinc-700"></div>
                  <div className="h-4 flex-1 rounded bg-zinc-700"></div>
                  <div className="h-4 w-16 rounded bg-zinc-600"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
