"use client";

import { motion } from "motion/react";

import AlgoliaBlueButton from "@/components/custom/ui/algolia-blue-button";

export default function CTASection() {
  return (
    <section className="bg-background relative px-4 py-32 sm:px-6 lg:px-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, amount: 0.1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="mx-auto max-w-4xl text-center"
      >
        {/* Main CTA Title - Linear Style */}
        <h2 className="from-primary via-primary/80 to-primary/70 bg-gradient-to-r bg-clip-text text-5xl font-medium tracking-tight text-transparent sm:text-6xl">
          Plan the present. Build the future.
        </h2>

        {/* CTA Buttons - Linear Style */}
        <div className="mt-12 flex flex-col gap-3 sm:flex-row sm:justify-center">
          <button
            className="text-foreground bg-background hover:bg-muted/50 dark:ring-input border-input/50 dark:border-input relative h-12 cursor-pointer rounded-lg border-b-2 px-6 py-3 text-sm font-medium shadow-sm ring-1 shadow-zinc-950/15 ring-zinc-300 transition-all duration-200"
            role="button"
          >
            Talk to sales
          </button>
          <AlgoliaBlueButton>Get Started</AlgoliaBlueButton>
        </div>
      </motion.div>
    </section>
  );
}
