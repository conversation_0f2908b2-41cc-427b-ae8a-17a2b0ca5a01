"use client";

import { RiSkipRightLine } from "@remixicon/react";
import { usePathname } from "next/navigation";

import { Session } from "@/auth";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import UserAvatar from "@/components/user-avatar";

// Breadcrumb configuration
interface BreadcrumbItem {
  label: string;
  href?: string;
}

const breadcrumbConfig: Record<string, BreadcrumbItem[]> = {
  "/dashboard": [{ label: "Dashboard" }],
};

function DynamicBreadcrumb() {
  const pathname = usePathname();

  // Handle dynamic document preview routes
  let breadcrumbs = breadcrumbConfig[pathname];

  // Fallback to dashboard
  if (!breadcrumbs) {
    breadcrumbs = [{ label: "Dashboard" }];
  }

  if (breadcrumbs.length === 1) {
    return (
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbPage>{breadcrumbs[0].label}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    );
  }

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {breadcrumbs.map((item, index) => {
          const isLast = index === breadcrumbs.length - 1;

          return (
            <div
              key={index}
              className="flex items-center space-x-2"
            >
              {index > 0 && <BreadcrumbSeparator className="hidden md:block" />}
              <BreadcrumbItem className={!isLast ? "hidden md:block" : ""}>
                {isLast || !item.href ? (
                  <BreadcrumbPage>{item.label}</BreadcrumbPage>
                ) : (
                  <BreadcrumbLink href={item.href || "#"}>
                    {item.label}
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
            </div>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
}

interface ClientSidebarLayoutProps {
  children: React.ReactNode;
  session: Session;
}

export default function ClientSidebarLayout({
  children,
  session,
}: ClientSidebarLayoutProps) {
  return (
    <>
      <header className="bg-background sticky top-0 z-50 flex shrink-0 items-center gap-2 border-b p-4 transition-[margin-right] duration-200 ease-in-out">
        <SidebarTrigger className="-ml-1">
          <RiSkipRightLine className="!size-4" />
        </SidebarTrigger>
        <Separator
          orientation="vertical"
          className="mr-2 data-[orientation=vertical]:h-4"
        />
        <DynamicBreadcrumb />

        <div className="ml-auto flex flex-row items-center gap-3">
          <UserAvatar session={session} />
        </div>
      </header>
      <main className="relative flex flex-1 flex-col gap-4 overflow-y-auto p-4 transition-[margin-right] duration-200 ease-in-out md:p-6 lg:p-8">
        {children}
      </main>
    </>
  );
}
