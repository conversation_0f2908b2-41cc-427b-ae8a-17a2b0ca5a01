import type { Metadata } from "next";
import Image from "next/image";

import ThemeSwitcher from "@/components/theme-switcher";

export const metadata: Metadata = {
  title: "Authentication",
  description: "Sign in or create an account",
};

export default function AuthLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="flex min-h-screen items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-[380px] space-y-8">
        <Image
          src="/ua-logo.png"
          alt="Logo"
          width={36}
          height={36}
          quality={100}
          priority
          className="mx-auto"
        />

        {children}
      </div>
      <ThemeSwitcher triggerClassName="absolute top-6 right-6" />
    </div>
  );
}
