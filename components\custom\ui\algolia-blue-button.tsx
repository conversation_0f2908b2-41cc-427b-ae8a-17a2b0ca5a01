export default function AlgoliaBlueButton({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <button
      className="relative box-border inline-flex h-12 cursor-pointer touch-manipulation items-center justify-center overflow-hidden rounded-md border-0 border-b-2 border-zinc-950/40 bg-linear-to-t bg-gradient-to-r from-sky-500 to-blue-400 px-5 py-6 text-sm leading-none whitespace-nowrap text-white no-underline shadow-md ring-1 shadow-[rgba(45,35,66,0.4)_0_2px_4px,rgba(45,35,66,0.3)_0_7px_13px_-3px,rgba(58,65,111,0.5)_0_-3px_0_inset] ring-white/25 transition-all duration-150 ease-in-out ring-inset hover:shadow-[rgba(45,35,66,0.4)_0_4px_8px,rgba(45,35,66,0.3)_0_7px_13px_-3px,#3c4fe0_0_-3px_0_inset] hover:brightness-110 focus:shadow-[#3c4fe0_0_0_0_1.5px_inset,rgba(45,35,66,0.4)_0_2px_4px,rgba(45,35,66,0.3)_0_7px_13px_-3px,#3c4fe0_0_-3px_0_inset] active:translate-y-0.5 active:shadow-[#3c4fe0_0_3px_7px_inset] active:brightness-90 dark:border-x-0 dark:border-t-0 dark:border-zinc-950/50 dark:inset-shadow-2xs dark:ring-white/5 dark:inset-shadow-white/10"
      role="button"
    >
      {children}
    </button>
  );
}
