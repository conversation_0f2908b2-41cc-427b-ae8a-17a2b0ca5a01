"use client";

import * as React from "react";

import { MonitorIcon, MoonIcon, SunIcon } from "lucide-react";
import { useTheme } from "next-themes";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";

const themes = [
  { id: "light", label: "Light Mode", icon: SunIcon },
  { id: "dark", label: "Dark Mode", icon: MoonIcon },
  { id: "system", label: "System", icon: MonitorIcon },
] as const;

type ThemeSwitcherProps = {
  triggerClassName?: string;
  contentClassName?: string;
};

export default function ThemeSwitcher({
  triggerClassName,
  contentClassName,
}: ThemeSwitcherProps) {
  const { theme, setTheme } = useTheme();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="icon"
          className={cn(
            "rounded-full transition-all duration-200",
            triggerClassName,
          )}
          aria-label="Toggle theme"
        >
          <SunIcon className="h-4 w-4 scale-100 rotate-0 transition-transform duration-200 ease-in-out dark:scale-0 dark:-rotate-90" />
          <MoonIcon className="absolute h-4 w-4 scale-0 rotate-90 transition-transform duration-200 ease-in-out dark:scale-100 dark:rotate-0" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        align="end"
        sideOffset={8}
        className={cn(
          "bg-background/95 border-border/50 w-48 border p-2 shadow-xl backdrop-blur-xl",
          contentClassName,
        )}
        forceMount
      >
        {themes.map(({ id, label, icon: Icon }) => {
          const isActive = theme === id;
          return (
            <DropdownMenuItem
              key={id}
              onClick={() => setTheme(id)}
              className={cn(
                "hover:bg-muted/50 focus:bg-muted/50 flex cursor-pointer items-center gap-3 truncate !rounded-lg px-3 py-2 text-sm font-medium transition-all duration-200",
                isActive && "text-primary",
              )}
            >
              <Icon
                className={cn(
                  "h-4 w-4 transition-colors",
                  isActive ? "text-primary" : "text-muted-foreground",
                )}
              />
              <span>{label}</span>
              {isActive && (
                <div className="bg-primary ml-auto h-1.5 w-1.5 rounded-full" />
              )}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
