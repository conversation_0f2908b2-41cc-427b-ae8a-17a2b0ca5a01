"use client";

import {
  ArrowRightIcon,
  Code,
  Rocket,
  Shield,
  Sparkles,
  Users,
  Zap,
} from "lucide-react";
import { motion } from "motion/react";

const features = [
  {
    id: "main",
    title: "Purpose-built for product development",
    description:
      "Next Core is shaped by the practices and principles that distinguish world-class product teams from the rest: relentless focus, fast execution, and a commitment to the quality of craft.",
    icon: Code,
    size: "large",
    visual: (
      <div className="relative h-full w-full overflow-hidden">
        <div className="from-primary/10 via-primary/5 absolute inset-0 bg-gradient-to-br to-transparent"></div>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="relative">
            <motion.div
              className="bg-primary/20 border-primary/30 h-32 w-40 rounded-2xl border backdrop-blur-sm"
              animate={{
                rotateY: [0, 5, 0],
                rotateX: [0, -2, 0],
              }}
              transition={{
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            <motion.div
              className="bg-primary/30 border-primary/40 absolute -top-4 -right-4 h-20 w-24 rounded-xl border backdrop-blur-sm"
              animate={{
                rotateY: [0, -3, 0],
                rotateX: [0, 2, 0],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 1,
              }}
            />
            <motion.div
              className="bg-primary/40 absolute bottom-2 left-2 h-6 w-6 rounded-full"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.6, 1, 0.6],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
          </div>
        </div>
      </div>
    ),
  },
  {
    id: "speed",
    title: "Lightning Fast",
    description:
      "Built for speed with optimized performance and instant loading.",
    icon: Zap,
    size: "medium",
    visual: (
      <div className="relative h-full w-full overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/10 via-orange-500/5 to-transparent"></div>
        <div className="absolute inset-0 flex items-center justify-center">
          <motion.div
            className="relative"
            animate={{
              scale: [1, 1.05, 1],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            <Zap className="h-16 w-16 text-yellow-500" />
            <motion.div
              className="absolute -inset-4 rounded-full border-2 border-yellow-500/30"
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.5, 0, 0.5],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeOut",
              }}
            />
          </motion.div>
        </div>
      </div>
    ),
  },
  {
    id: "secure",
    title: "Enterprise Security",
    description:
      "Bank-grade security with end-to-end encryption and compliance.",
    icon: Shield,
    size: "medium",
    visual: (
      <div className="relative h-full w-full overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 via-emerald-500/5 to-transparent"></div>
        <div className="absolute inset-0 flex items-center justify-center">
          <motion.div
            className="relative"
            animate={{
              rotateY: [0, 360],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "linear",
            }}
          >
            <Shield className="h-16 w-16 text-green-500" />
          </motion.div>
        </div>
      </div>
    ),
  },
  {
    id: "deploy",
    title: "One-Click Deploy",
    description: "Deploy anywhere with a single command.",
    icon: Rocket,
    size: "small",
    visual: (
      <div className="relative h-full w-full overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-pink-500/5 to-transparent"></div>
        <div className="absolute inset-0 flex items-center justify-center">
          <motion.div
            animate={{
              y: [0, -10, 0],
              rotate: [0, 5, 0],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            <Rocket className="h-12 w-12 text-purple-500" />
          </motion.div>
        </div>
      </div>
    ),
  },
  {
    id: "team",
    title: "Team Collaboration",
    description: "Built for teams with real-time collaboration features.",
    icon: Users,
    size: "small",
    visual: (
      <div className="relative h-full w-full overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-cyan-500/5 to-transparent"></div>
        <div className="absolute inset-0 flex items-center justify-center">
          <motion.div
            animate={{
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            <Users className="h-12 w-12 text-blue-500" />
          </motion.div>
        </div>
      </div>
    ),
  },
  {
    id: "ai",
    title: "AI-Powered",
    description: "Smart automation and intelligent suggestions powered by AI.",
    icon: Sparkles,
    size: "wide",
    visual: (
      <div className="relative h-full w-full overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/10 via-purple-500/5 to-transparent"></div>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="relative">
            <motion.div
              animate={{
                rotate: [0, 360],
              }}
              transition={{
                duration: 10,
                repeat: Infinity,
                ease: "linear",
              }}
            >
              <Sparkles className="h-14 w-14 text-indigo-500" />
            </motion.div>
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute h-2 w-2 rounded-full bg-indigo-400"
                style={{
                  top: `${20 + Math.sin((i * Math.PI) / 3) * 30}px`,
                  left: `${20 + Math.cos((i * Math.PI) / 3) * 30}px`,
                }}
                animate={{
                  scale: [0, 1, 0],
                  opacity: [0, 1, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: i * 0.3,
                }}
              />
            ))}
          </div>
        </div>
      </div>
    ),
  },
];

// Helper function to get grid classes based on feature size
const getGridClasses = (size: string) => {
  switch (size) {
    case "large":
      return "col-span-2 row-span-2";
    case "wide":
      return "col-span-2 row-span-1";
    case "medium":
      return "col-span-1 row-span-2";
    case "small":
      return "col-span-1 row-span-1";
    default:
      return "col-span-1 row-span-1";
  }
};

export default function FeaturesSection() {
  return (
    <section className="bg-muted/30 relative px-4 py-24 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        {/* Section Header */}
        <div className="mb-16 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <h2 className="from-primary via-primary/80 to-primary/70 mb-6 bg-gradient-to-r bg-clip-text text-5xl leading-tight font-medium text-transparent">
              Made for modern product teams
            </h2>
            <p className="text-muted-foreground mx-auto max-w-3xl text-lg leading-relaxed font-normal">
              Next Core is shaped by the practices and principles that
              distinguish world-class product teams from the rest: relentless
              focus, fast execution, and a commitment to the quality of craft.
            </p>
          </motion.div>
        </div>

        {/* Bento Grid */}
        <div className="grid auto-rows-[200px] grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <motion.div
                key={feature.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{
                  once: true,
                  amount: 0.2,
                  margin: "0px 0px -100px 0px",
                }}
                transition={{
                  duration: 0.8,
                  ease: [0.25, 0.1, 0.25, 1],
                  delay: index * 0.1,
                }}
                className={`group bg-card dark:bg-card/50 border-border hover:shadow-primary/5 relative overflow-hidden rounded-2xl border backdrop-blur-sm transition-all duration-300 hover:shadow-lg ${getGridClasses(feature.size)}`}
              >
                {/* Visual Background */}
                <div className="absolute inset-0">{feature.visual}</div>

                {/* Content Overlay */}
                <div className="relative z-10 flex h-full flex-col justify-between p-6">
                  {/* Icon */}
                  <div className="flex items-start justify-between">
                    <div className="bg-background/80 border-border/50 rounded-lg border p-2 backdrop-blur-sm">
                      <Icon className="text-foreground h-5 w-5" />
                    </div>
                  </div>

                  {/* Content */}
                  <div className="space-y-2">
                    <h3 className="text-foreground text-lg leading-tight font-semibold">
                      {feature.title}
                    </h3>
                    <p className="text-muted-foreground text-sm leading-relaxed font-normal">
                      {feature.description}
                    </p>
                  </div>
                </div>

                {/* Hover Effect */}
                <div className="from-background/20 absolute inset-0 bg-gradient-to-t via-transparent to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
              </motion.div>
            );
          })}
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.3 }}
          className="mt-16 text-center"
        >
          <div className="flex items-center justify-center">
            <span className="text-primary flex cursor-pointer items-center gap-2 text-sm font-medium hover:underline">
              Explore all features <ArrowRightIcon className="h-3 w-3" />
            </span>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
