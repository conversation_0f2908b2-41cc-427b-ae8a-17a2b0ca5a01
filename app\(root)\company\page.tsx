"use client";

import { useRef } from "react";

import { ArrowRightIcon } from "lucide-react";
import { motion } from "motion/react";

export default function CompanyPage() {
  // Type-safe useRef for scrolling to journey section
  const journeySectionRef = useRef<HTMLElement>(null);

  // Scroll to journey section
  const handleScrollToJourney = () => {
    if (journeySectionRef.current) {
      journeySectionRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <div className="relative bg-zinc-950">
      {/* Hero Section */}
      <section className="px-4 py-16 sm:px-6 lg:px-8 lg:py-24">
        <div className="mx-auto max-w-6xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="mb-12 text-center"
          >
            <h1 className="from-primary via-primary/80 to-primary/70 mb-4 bg-gradient-to-r bg-clip-text text-3xl leading-tight font-medium tracking-tight text-transparent sm:text-4xl lg:text-5xl">
              Innovate. Inspire. Impact.
            </h1>
            <p className="text-muted-foreground mx-auto max-w-2xl text-base leading-relaxed sm:text-lg">
              At Horizon Labs, we craft tools that empower creators to shape the
              future with precision and passion.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
            className="flex justify-center gap-4"
          >
            <button
              onClick={handleScrollToJourney}
              className="bg-primary text-primary-foreground hover:bg-primary/90 cursor-pointer rounded-md px-6 py-3 text-sm font-medium transition-colors"
            >
              Our Journey
            </button>
            <button className="text-primary hover:text-primary/80 flex items-center gap-2 text-sm font-medium transition-colors">
              Explore Careers
              <ArrowRightIcon className="h-4 w-4" />
            </button>
          </motion.div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="bg-zinc-900 px-4 py-16 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-6xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="mb-12 text-center"
          >
            <h2 className="mb-4 text-2xl font-medium text-zinc-200">
              Our Vision
            </h2>
            <p className="text-muted-foreground mx-auto max-w-3xl text-base leading-relaxed sm:text-lg">
              We believe technology should amplify human creativity, not
              constrain it. Our mission is to build intuitive, scalable tools
              that transform ideas into reality with unmatched elegance.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Journey/Timeline Section */}
      <section
        ref={journeySectionRef}
        className="px-4 py-16 sm:px-6 lg:px-8"
      >
        <div className="mx-auto max-w-6xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="mb-12 text-center"
          >
            <h2 className="mb-6 text-2xl font-medium text-zinc-200">
              Our Journey
            </h2>
            <p className="text-muted-foreground mx-auto max-w-2xl text-base leading-relaxed sm:text-lg">
              From a bold idea to a global impact, here’s how we’ve evolved.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
            className="relative"
          >
            <div className="absolute left-1/2 h-full w-1 -translate-x-1/2 transform bg-zinc-800"></div>
            {[
              {
                year: "2020",
                title: "Founded",
                desc: "Horizon Labs was born with a vision to redefine SaaS tools.",
              },
              {
                year: "2022",
                title: "First Product Launch",
                desc: "Released our flagship platform, empowering 10,000+ creators.",
              },
              {
                year: "2024",
                title: "Global Expansion",
                desc: "Scaled operations to serve users in 50+ countries.",
              },
            ].map((milestone, index) => (
              <div
                key={index}
                className={`mb-12 flex items-center ${index % 2 === 0 ? "flex-row" : "flex-row-reverse"}`}
              >
                <div className="w-1/2 px-4">
                  <motion.div
                    initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, ease: "easeOut", delay: 0.4 }}
                    className="rounded-lg border border-zinc-800 bg-zinc-900 p-6"
                  >
                    <h3 className="mb-2 text-lg font-medium text-zinc-200">
                      {milestone.year}: {milestone.title}
                    </h3>
                    <p className="text-muted-foreground text-sm">
                      {milestone.desc}
                    </p>
                  </motion.div>
                </div>
                <div className="w-1/2"></div>
              </div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Team Section */}
      <section className="bg-zinc-900 px-4 py-16 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-6xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="mb-12 text-center"
          >
            <h2 className="mb-6 text-2xl font-medium text-zinc-200">
              Our Team
            </h2>
            <p className="text-muted-foreground mx-auto max-w-2xl text-base leading-relaxed sm:text-lg">
              Meet the passionate individuals driving our vision forward.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
            className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3"
          >
            {[
              {
                name: "Jane Doe",
                role: "CEO & Founder",
                desc: "Leading with vision and innovation.",
              },
              {
                name: "John Smith",
                role: "CTO",
                desc: "Architecting our technical future.",
              },
              {
                name: "Emily Johnson",
                role: "Head of Design",
                desc: "Crafting user-centric experiences.",
              },
            ].map((member, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.05, transition: { duration: 0.3 } }}
                className="rounded-lg border border-zinc-800 bg-zinc-900 p-6 transition-colors hover:bg-zinc-800"
              >
                <div className="mx-auto mb-4 h-32 w-32 rounded-full bg-zinc-700"></div>
                <h3 className="mb-2 text-lg font-medium text-zinc-200">
                  {member.name}
                </h3>
                <p className="text-primary mb-2 text-sm">{member.role}</p>
                <p className="text-muted-foreground text-sm">{member.desc}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="bg-zinc-950 px-4 py-16 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-6xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="text-center"
          >
            <h2 className="mb-4 text-2xl font-medium text-zinc-200">
              Get in Touch
            </h2>
            <p className="text-muted-foreground mx-auto mb-6 max-w-2xl text-base">
              Have questions or want to collaborate? Reach out to us.
            </p>
            <button className="bg-primary text-primary-foreground hover:bg-primary/90 cursor-pointer rounded-md px-6 py-3 text-sm font-medium transition-colors">
              Contact Us
            </button>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
